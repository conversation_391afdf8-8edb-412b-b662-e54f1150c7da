# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
APP_NAME="YouTube Subtitles and Text Summarization API"
APP_VERSION="0.1.0"

# =============================================================================
# SERVER SETTINGS
# =============================================================================
HOST=0.0.0.0
PORT=8000
RELOAD=false

# =============================================================================
# SECURITY SETTINGS - CRITICAL FOR PRODUCTION!
# =============================================================================

# CORS Settings - SECURITY CRITICAL!
# ⚠️  WARNING: Using "*" allows ALL domains - SECURITY RISK in production!
# ✅ PRODUCTION: Set specific domains like: https://yourdomain.com,https://app.yourdomain.com
CORS_ORIGINS=*
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOW_HEADERS=Authorization,Content-Type,X-API-Key

# Security Headers
SECURITY_HEADERS_ENABLED=true

# Authentication
REQUIRE_AUTH=true
# API_KEYS={"admin_key": {"name": "Admin", "permissions": ["admin"], "rate_limit": 10000}}

# =============================================================================
# PROXY SETTINGS (optional)
# =============================================================================
# SOCKS5_PROXY=socks5://127.0.0.1:1080

# Proxy Configuration (optional)
# Format: socks5://host:port
SOCKS5_PROXY=
# yt-dlp Cookies File (optional)
# Path to a Netscape-format cookies file
# COOKIES_FILE=/path/to/your/cookies.txt
COOKIES_FILE=

# =============================================================================
# AI SETTINGS
# =============================================================================

# Gemini API Configuration
GEM_API=YOUR_API_KEY

# =============================================================================
# DATABASE SETTINGS
# =============================================================================

DATABASE_TYPE=postgres
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=yt_subs

# =============================================================================
# LOGGING SETTINGS
# =============================================================================

# LOG_LEVEL: DEBUG, INFO, WARNING, ERROR, CRITICAL (default: INFO)
LOG_LEVEL=DEBUG
DEBUG=true
STRUCTURED_LOGGING=true
LOG_JSON_FORMAT=false
LOG_REQUEST_BODY=false
LOG_RESPONSE_BODY=false

# =============================================================================
# RATES SETTINGS
# =============================================================================

# Rate Limiting Configuration
RATE_LIMITING_ENABLED=true

# Anonymous user limits (per minute/hour/day)
ANONYMOUS_RATE_LIMIT_MINUTE=10
ANONYMOUS_RATE_LIMIT_HOUR=100
ANONYMOUS_RATE_LIMIT_DAY=500

# Read-only user limits
READ_RATE_LIMIT_MINUTE=30
READ_RATE_LIMIT_HOUR=500
READ_RATE_LIMIT_DAY=2000

# Read-write user limits
WRITE_RATE_LIMIT_MINUTE=60
WRITE_RATE_LIMIT_HOUR=1000
WRITE_RATE_LIMIT_DAY=5000

# Admin user limits
ADMIN_RATE_LIMIT_MINUTE=120
ADMIN_RATE_LIMIT_HOUR=2000
ADMIN_RATE_LIMIT_DAY=10000

# Endpoint-specific limits
SUBTITLES_RATE_LIMIT_MINUTE=30
SUBTITLES_RATE_LIMIT_HOUR=200
SUBTITLES_RATE_LIMIT_DAY=1000

SUMMARIZE_RATE_LIMIT_MINUTE=5
SUMMARIZE_RATE_LIMIT_HOUR=50
SUMMARIZE_RATE_LIMIT_DAY=400