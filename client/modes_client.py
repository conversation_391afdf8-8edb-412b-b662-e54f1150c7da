from typing import List, Literal

# Доступные режимы суммаризации
AVAILABLE_MODES = [
    "default",
    "default#2",
    "default#3",
    "short#1",
    "short#2",
    "socratic#1",
    "socratic#2",
    "socratic#3",
    "socratic#4",
    "socratic#5",
    "diablo#1",
    "diablo#2",
    "diablo#3",
    "diablo#4",
    "diablo#5",
    "diablo#6",
    "diablo#7",
    "detailed#1",
    "detailed#2",
    "detailed#3",
    "detailed#4",
    "detailed#5",
    "detailed#6",
    "detailed#7",
    "detailed#8",
    "detailed#9",
    "detailed#10",
    "detailed#11",
    "detailed#12",
    "detailed#13",
    "detailed#14",
    "detailed#15",
    "detailed#16",
    "detailed#17",
    "detailed#18",
    "detailed#19",
    "fireship#1",
    "fireship#2",
    "fireship#3",
    "stevejobs#1",
]

# Тип для режимов суммаризации
SummarizationMode = Literal[AVAILABLE_MODES]


def get_available_modes() -> List[str]:
    """Получить список доступных режимов суммаризации"""
    return AVAILABLE_MODES


def is_valid_mode(mode: str) -> bool:
    """Проверить, является ли режим допустимым"""
    return mode in AVAILABLE_MODES
