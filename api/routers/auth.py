"""
Authentication and API key management endpoints.
"""
from typing import List, Dict, Any
from fastapi import APIRouter, Depends
from pydantic import BaseModel, Field, validator
from loguru import logger

from api.middleware.auth import (
    api_key_manager,
    get_current_user,
    require_admin,
    require_read,
)

router = APIRouter()


class APIKeyCreateRequest(BaseModel):
    """Request model for creating a new API key with validation."""
    name: str = Field(..., min_length=3, max_length=100, description="Human-readable name for the API key")
    permissions: List[str] = Field(default=["read"], min_items=1, max_items=3, description="List of permissions")
    rate_limit: int = Field(default=100, ge=1, le=10000, description="Rate limit per hour")

    @validator('name')
    def validate_name(cls, v):
        """Validate API key name."""
        if not v or not v.strip():
            raise ValueError("Name cannot be empty")

        # Check for potentially problematic characters
        import re
        if re.search(r'[<>"\']', v):
            raise ValueError("Name cannot contain HTML/script characters")

        return v.strip()

    @validator('permissions')
    def validate_permissions(cls, v):
        """Validate permissions list."""
        valid_permissions = ["read", "write", "admin"]
        invalid_perms = [p for p in v if p not in valid_permissions]
        if invalid_perms:
            raise ValueError(f"Invalid permissions: {invalid_perms}. Valid: {valid_permissions}")

        # Remove duplicates while preserving order
        seen = set()
        unique_perms = []
        for perm in v:
            if perm not in seen:
                seen.add(perm)
                unique_perms.append(perm)

        return unique_perms


class APIKeyResponse(BaseModel):
    """Response model for API key operations."""
    api_key: str
    name: str
    permissions: List[str]
    rate_limit: int
    message: str


class UserInfoResponse(BaseModel):
    """Response model for user information."""
    name: str
    permissions: List[str]
    rate_limit: int
    api_key_preview: str


@router.get("/auth/me", response_model=UserInfoResponse)
async def get_current_user_info(user: Dict[str, Any] = Depends(require_read)):
    """
    Get information about the current authenticated user.

    Requires: read permission
    """
    logger.debug(f"User info requested by: {user['name']}")

    return UserInfoResponse(
        name=user['name'],
        permissions=user['permissions'],
        rate_limit=user['rate_limit'],
        api_key_preview=f"{user['api_key'][:10]}..."
    )


@router.get("/auth/keys")
async def list_api_keys(user: Dict[str, Any] = Depends(require_admin)):
    """
    List all API keys (admin only).

    Requires: admin permission
    """
    logger.info(f"API keys list requested by admin: {user['name']}")

    keys = api_key_manager.list_api_keys()
    return {
        "api_keys": keys,
        "total": len(keys),
        "message": "API keys retrieved successfully"
    }


@router.post("/auth/keys", response_model=APIKeyResponse)
async def create_api_key(
    request: APIKeyCreateRequest,
    user: Dict[str, Any] = Depends(require_admin)
):
    """
    Create a new API key (admin only).

    Requires: admin permission
    """
    logger.info(f"New API key creation requested by admin: {user['name']} for user: {request.name}")

    # Note: Validation is now handled by the Pydantic model

    # Generate new API key
    new_api_key = api_key_manager.generate_api_key(
        name=request.name,
        permissions=request.permissions,
        rate_limit=request.rate_limit
    )

    logger.info(f"New API key created for {request.name} by {user['name']}")

    return APIKeyResponse(
        api_key=new_api_key,
        name=request.name,
        permissions=request.permissions,
        rate_limit=request.rate_limit,
        message="API key created successfully"
    )


@router.delete("/auth/keys/{api_key_preview}")
async def revoke_api_key(
    api_key_preview: str,
    user: Dict[str, Any] = Depends(require_admin)
):
    """
    Revoke an API key (admin only).

    Args:
        api_key_preview: First 10 characters of the API key to revoke

    Requires: admin permission
    """
    logger.info(f"API key revocation requested by admin: {user['name']} for key: {api_key_preview}")

    # Find the full API key by preview
    all_keys = api_key_manager._api_keys
    target_key = None

    for key in all_keys.keys():
        if key.startswith(api_key_preview.replace("...", "")):
            target_key = key
            break

    if not target_key:
        logger.warning(f"API key not found for preview: {api_key_preview}")
        return {
            "success": False,
            "message": f"API key not found: {api_key_preview}"
        }

    # Prevent self-revocation
    if target_key == user['api_key']:
        logger.warning(f"Admin {user['name']} attempted to revoke their own key")
        return {
            "success": False,
            "message": "Cannot revoke your own API key"
        }

    success = api_key_manager.revoke_api_key(target_key)

    if success:
        logger.info(f"API key {api_key_preview} revoked by {user['name']}")
        return {
            "success": True,
            "message": f"API key {api_key_preview} revoked successfully"
        }
    else:
        logger.error(f"Failed to revoke API key {api_key_preview}")
        return {
            "success": False,
            "message": f"Failed to revoke API key {api_key_preview}"
        }


@router.get("/auth/permissions")
async def get_available_permissions(user: Dict[str, Any] = Depends(require_read)):
    """
    Get list of available permissions.

    Requires: read permission
    """
    permissions = {
        "read": "Can read data and view information",
        "write": "Can create and modify data",
        "admin": "Can manage API keys and system settings"
    }

    return {
        "permissions": permissions,
        "user_permissions": user['permissions'],
        "message": "Available permissions retrieved successfully"
    }
