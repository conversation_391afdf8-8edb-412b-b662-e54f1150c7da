"""
Security headers middleware for FastAPI.

This module provides middleware to add security headers to HTTP responses
to protect against common web vulnerabilities.
"""

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from loguru import logger

from core.config import get_settings

settings = get_settings()


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """
    Middleware to add security headers to all HTTP responses.

    This middleware adds the following security headers:
    - X-Content-Type-Options: nosniff
    - X-Frame-Options: DENY
    - X-XSS-Protection: 1; mode=block
    - Strict-Transport-Security: max-age=31536000; includeSubDomains
    - Content-Security-Policy: default-src 'self'
    - Referrer-Policy: strict-origin-when-cross-origin
    - Permissions-Policy: geolocation=(), microphone=(), camera=()
    """

    def __init__(self, app, enabled: bool = True):
        super().__init__(app)
        self.enabled = enabled

        # Security headers configuration
        self.security_headers = {
            # Prevent MIME type sniffing
            "X-Content-Type-Options": "nosniff",
            # Prevent clickjacking
            "X-Frame-Options": "DENY",
            # Enable XSS protection (legacy, but still useful)
            "X-XSS-Protection": "1; mode=block",
            # Force HTTPS (only add if not in development)
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains"
            if not settings.DEBUG
            else None,
            # Content Security Policy (restrictive but safe)
            "Content-Security-Policy": (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self'; "
                "connect-src 'self'; "
                "frame-ancestors 'none'"
            ),
            # Referrer policy
            "Referrer-Policy": "strict-origin-when-cross-origin",
            # Permissions policy (disable potentially dangerous features)
            "Permissions-Policy": (
                "geolocation=(), "
                "microphone=(), "
                "camera=(), "
                "payment=(), "
                "usb=(), "
                "magnetometer=(), "
                "gyroscope=(), "
                "speaker=()"
            ),
            # Remove server information
            "Server": "FastAPI",
        }

        # Remove None values
        self.security_headers = {
            k: v for k, v in self.security_headers.items() if v is not None
        }

        # Log initialization
        if self.enabled:
            logger.info(
                f"🛡️ Security headers middleware initialized with {len(self.security_headers)} headers"
            )
            if settings.DEBUG:
                logger.debug(
                    f"🔧 Security headers: {', '.join(self.security_headers.keys())}"
                )
        else:
            logger.warning("⚠️ Security headers middleware is DISABLED")

    async def dispatch(self, request: Request, call_next) -> Response:
        """Add security headers to the response."""
        if not self.enabled:
            return await call_next(request)

        # Process the request
        response = await call_next(request)

        # Add security headers
        for header_name, header_value in self.security_headers.items():
            response.headers[header_name] = header_value

        # Log security headers addition (only in debug mode)
        if settings.DEBUG and settings.LOG_LEVEL == "DEBUG":
            logger.debug(
                f"🛡️ Security headers added to {request.url.path}: "
                f"{', '.join(self.security_headers.keys())}"
            )

        return response


def create_security_middleware(enabled: bool = True) -> SecurityHeadersMiddleware:
    """
    Create security headers middleware.

    Args:
        enabled: Whether to enable security headers

    Returns:
        Configured security headers middleware
    """
    return SecurityHeadersMiddleware(app=None, enabled=enabled)
