"""
Authentication middleware and utilities.
"""

import secrets
import time
from typing import Any

from fastapi import Depends, Request, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from loguru import logger

from api.middleware.error_handler import APIError, ErrorCode
from core.config import get_settings

settings = get_settings()
security = HTTPBearer(auto_error=False)


class AuthError(APIError):
    """Authentication error."""

    def __init__(self, message: str = "Authentication failed"):
        super().__init__(
            message=message,
            error_code=ErrorCode.UNAUTHORIZED,
            status_code=status.HTTP_401_UNAUTHORIZED,
        )


class ForbiddenError(APIError):
    """Authorization error."""

    def __init__(self, message: str = "Access forbidden"):
        super().__init__(
            message=message,
            error_code=ErrorCode.FORBIDDEN,
            status_code=status.HTTP_403_FORBIDDEN,
        )


class APIKeyManager:
    """Manages API keys and authentication."""

    def __init__(self):
        # In production, these should be stored in a database
        # For now, we'll use environment variables and in-memory storage
        self._api_keys: dict[str, dict[str, Any]] = {}
        self._load_default_keys()

    def _load_default_keys(self):
        """Load default API keys from environment or create demo keys."""
        # Load from environment if available
        if hasattr(settings, "API_KEYS") and settings.API_KEYS:
            for key_data in settings.API_KEYS:
                self._api_keys[key_data["key"]] = {
                    "name": key_data.get("name", "Unknown"),
                    "permissions": key_data.get("permissions", ["read", "write"]),
                    "rate_limit": key_data.get("rate_limit", 100),  # requests per hour
                    "active": key_data.get("active", True),
                }
        else:
            # Create demo keys for development
            demo_key = "demo_key_12345"
            admin_key = "admin_key_67890"

            self._api_keys[demo_key] = {
                "name": "Demo User",
                "permissions": ["read"],
                "rate_limit": 50,
                "active": True,
            }

            self._api_keys[admin_key] = {
                "name": "Admin User",
                "permissions": ["read", "write", "admin"],
                "rate_limit": 1000,
                "active": True,
            }

            logger.debug(
                f"Created demo API keys: {demo_key} (read-only), {admin_key} (admin)"
            )

    def validate_api_key(self, api_key: str) -> dict[str, Any] | None:
        """Validate an API key and return user info."""
        if not api_key:
            return None

        key_info = self._api_keys.get(api_key)
        if not key_info or not key_info.get("active", False):
            return None

        return key_info

    def generate_api_key(
        self, name: str, permissions: list = None, rate_limit: int = 100
    ) -> str:
        """Generate a new API key."""
        if permissions is None:
            permissions = ["read"]

        # Generate a secure random key
        api_key = f"ak_{secrets.token_urlsafe(32)}"

        self._api_keys[api_key] = {
            "name": name,
            "permissions": permissions,
            "rate_limit": rate_limit,
            "active": True,
        }

        logger.info(f"Generated new API key for {name}: {api_key[:10]}...")
        return api_key

    def revoke_api_key(self, api_key: str) -> bool:
        """Revoke an API key."""
        if api_key in self._api_keys:
            self._api_keys[api_key]["active"] = False
            logger.info(f"Revoked API key: {api_key[:10]}...")
            return True
        return False

    def list_api_keys(self) -> dict[str, dict[str, Any]]:
        """List all API keys (without exposing the full key)."""
        return {
            f"{key[:10]}...": {
                "name": info["name"],
                "permissions": info["permissions"],
                "rate_limit": info["rate_limit"],
                "active": info["active"],
            }
            for key, info in self._api_keys.items()
        }


# Global API key manager
api_key_manager = APIKeyManager()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> dict[str, Any]:
    """
    Get current authenticated user from API key.

    Args:
        credentials: HTTP Bearer token credentials

    Returns:
        User information dictionary

    Raises:
        AuthError: If authentication fails
    """
    if not credentials:
        raise AuthError("API key required")

    api_key = credentials.credentials
    user_info = api_key_manager.validate_api_key(api_key)

    if not user_info:
        logger.warning(f"Invalid API key attempt: {api_key[:10]}...")
        raise AuthError("Invalid API key")

    logger.debug(f"Authenticated user: {user_info['name']}")
    return {
        "api_key": api_key,
        "name": user_info["name"],
        "permissions": user_info["permissions"],
        "rate_limit": user_info["rate_limit"],
    }


async def get_optional_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> dict[str, Any] | None:
    """
    Get current user if authenticated, otherwise return None.
    Used for endpoints that have optional authentication.
    """
    if not credentials:
        return None

    try:
        return await get_current_user(credentials)
    except AuthError:
        return None


def require_permissions(*required_permissions: str):
    """
    Decorator to require specific permissions.

    Args:
        required_permissions: List of required permissions

    Returns:
        Dependency function that checks permissions
    """

    async def check_permissions(
        user: dict[str, Any] = Depends(get_current_user),
    ) -> dict[str, Any]:
        user_permissions = user.get("permissions", [])

        # Check if user has all required permissions
        missing_permissions = [
            perm for perm in required_permissions if perm not in user_permissions
        ]

        if missing_permissions:
            logger.warning(
                f"User {user['name']} missing permissions: {missing_permissions}. "
                f"Required: {required_permissions}, Has: {user_permissions}"
            )
            raise ForbiddenError(f"Missing required permissions: {missing_permissions}")

        return user

    return check_permissions


# Common permission dependencies
require_read = require_permissions("read")
require_write = require_permissions("write")
require_admin = require_permissions("admin")


async def auth_middleware(request: Request, call_next):
    """
    Authentication middleware that logs authentication attempts.
    """
    start_time = time.time()

    # Extract API key from Authorization header
    auth_header = request.headers.get("Authorization")
    api_key = None
    if auth_header and auth_header.startswith("Bearer "):
        api_key = auth_header[7:]  # Remove "Bearer " prefix

    # Add user info to request state if authenticated
    if api_key:
        user_info = api_key_manager.validate_api_key(api_key)
        if user_info:
            request.state.user = {
                "api_key": api_key,
                "name": user_info["name"],
                "permissions": user_info["permissions"],
                "rate_limit": user_info["rate_limit"],
            }

    response = await call_next(request)

    # Log authentication info with structured logging
    duration = time.time() - start_time
    user_info = getattr(request.state, "user", {})
    user_name = user_info.get("name", "Anonymous")

    # Use structured logging if available
    try:
        import structlog

        struct_logger = structlog.get_logger("api.middleware.auth")
        struct_logger.info(
            "auth_request_processed",
            method=request.method,
            path=request.url.path,
            user=user_name,
            user_id=user_info.get("id"),
            permissions=user_info.get("permissions", []),
            status_code=response.status_code,
            duration=round(duration, 4),
            authenticated=hasattr(request.state, "user") and bool(request.state.user),
        )
    except ImportError:
        # Fall back to loguru
        logger.info(
            "Request processed",
            extra={
                "method": request.method,
                "path": request.url.path,
                "user": user_name,
                "status_code": response.status_code,
                "duration": duration,
            },
        )

    return response
