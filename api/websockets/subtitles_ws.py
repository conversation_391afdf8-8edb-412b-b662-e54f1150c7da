"""
WebSocket endpoints for subtitle extraction.
"""
import asyncio
from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from loguru import logger

from models.schemas import (
    WebSocketSubtitleMessage,
    MessageType,
    TaskStatus,
)
from models.validators import validate_youtube_url
from core.config import get_settings

settings = get_settings()
PING_INTERVAL = settings.WEBSOCKET_PING_INTERVAL

router = APIRouter()


def _check_websocket_overload(task_queue) -> tuple[bool, dict]:
    """Check if server is overloaded for WebSocket connections."""
    active_tasks_count = len(task_queue.active_async_tasks)
    max_total_workers = (
        task_queue.LIMIT_SUBTITLE_WORKERS + task_queue.LIMIT_SUMMARIZE_WORKERS
    )

    if active_tasks_count >= max_total_workers:
        return True, {
            "type": MessageType.STATUS,
            "task_id": "error",
            "status": TaskStatus.FAILED,
            "error": "Server is at maximum processing capacity. Please try again later.",
        }

    return False, {}


@router.websocket("/ws/subtitles")
async def websocket_subtitles_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time subtitle extraction updates"""
    logger.debug(
        f"New WebSocket connection attempt for /ws/subtitles from {websocket.client}"
    )

    # Get task_queue from app state (will be set during lifespan)
    task_queue = websocket.app.state.task_queue

    # Check server overload
    is_overloaded, error_response = _check_websocket_overload(task_queue)
    if is_overloaded:
        logger.debug(
            f"Overload check failed for /ws/subtitles endpoint: Server is at maximum processing capacity."
        )
        await websocket.accept()
        await websocket.send_json(
            WebSocketSubtitleMessage(**error_response).model_dump()
        )
        await websocket.close()
        logger.debug(
            f"WebSocket connection closed due to overload for /ws/subtitles from {websocket.client}"
        )
        return

    await websocket.accept()
    logger.debug(
        f"WebSocket connected: {websocket.client} for /ws/subtitles endpoint"
    )
    task_id = None

    try:
        while True:
            try:
                client_message = await asyncio.wait_for(
                    websocket.receive_json(), timeout=PING_INTERVAL
                )
                logger.debug(
                    f"WebSocket /ws/subtitles endpoint received raw message: {client_message} from {websocket.client}"
                )

                # Handle pong messages
                if client_message.get("type") == "pong" and "task_id" in client_message:
                    logger.debug(
                        f"Handling pong from {websocket.client} for task {client_message['task_id']} in /ws/subtitles endpoint"
                    )
                    if task_id and client_message["task_id"] == task_id:
                        await task_queue.ws_manager.handle_pong(
                            client_message["task_id"], websocket
                        )
                        logger.debug(
                            f"Pong successfully processed for task {task_id} from {websocket.client} in /ws/subtitles endpoint"
                        )
                    elif not task_id and client_message["task_id"] == "initial":
                        await task_queue.ws_manager.handle_pong(
                            client_message["task_id"], websocket
                        )
                        logger.debug(
                            f"Initial pong successfully processed from {websocket.client} in /ws/subtitles endpoint"
                        )
                    else:
                        logger.warning(
                            f"Pong received for mismatched task_id from {websocket.client} in /ws/subtitles endpoint: expected {task_id}, got {client_message['task_id']}"
                        )
                    continue

                # Handle URL submission
                if "url" in client_message:
                    url_submitted = client_message["url"]
                    logger.debug(
                        f"Client {websocket.client} submitted URL via WebSocket /ws/subtitles endpoint: {url_submitted}"
                    )
                    try:
                        # Validate YouTube URL format
                        validated_url = validate_youtube_url(url_submitted)
                        logger.debug(f"URL validation passed for: {validated_url}")

                    except ValueError as validation_error:
                        logger.warning(f"URL validation failed for {url_submitted}: {validation_error}")
                        await websocket.send_json(
                            WebSocketSubtitleMessage(
                                type=MessageType.STATUS,
                                task_id="error",
                                status=TaskStatus.FAILED,
                                error=f"Invalid YouTube URL: {str(validation_error)}",
                                client_uid=None,
                            ).model_dump()
                        )
                        continue

                    try:
                        # Note: client_uid is deprecated and ignored in processing
                        # We still extract it for backward compatibility but don't use it
                        client_uid = client_message.get("client_uid")
                        if client_uid:
                            logger.debug(
                                f"Received deprecated client_uid: {client_uid} (ignored for processing)"
                            )

                        logger.debug(
                            f"Attempting to add task for {validated_url} to subtitle queue via WebSocket /ws/subtitles endpoint."
                        )
                        response = await task_queue.add_subtitle_task(
                            video_id=validated_url,
                            url=validated_url,
                            client_uid=None  # Explicitly set to None - deprecated field
                        )
                        logger.debug(
                            f"Task add response for {validated_url} in /ws/subtitles endpoint: ID {response.task_id}, Status {response.status}"
                        )

                        if response.task_id != "error":
                            if task_id != response.task_id:
                                if task_id:
                                    logger.debug(
                                        f"Task ID changed for {websocket.client} in /ws/subtitles endpoint. Old: {task_id}, New: {response.task_id}. Disconnecting from old if necessary."
                                    )
                                    await task_queue.ws_manager.disconnect_from_task(
                                        task_id, websocket
                                    )
                            task_id = response.task_id
                            logger.debug(
                                f"Registering websocket {websocket.client} for task {task_id} in /ws/subtitles endpoint"
                            )
                            await task_queue.register_websocket(task_id, websocket)

                        await websocket.send_json(
                            WebSocketSubtitleMessage(
                                type=MessageType.STATUS,
                                task_id=response.task_id,
                                status=response.status,
                                title=response.title,
                                original_language=response.original_language,
                                publish_date=response.publish_date,
                                en_subtitles=response.en_subtitles,
                                ru_subtitles=response.ru_subtitles,
                                error=response.error,
                                client_uid=None,  # Don't include deprecated field in new responses
                            ).model_dump()
                        )
                        logger.debug(
                            f"Sent status update for task {response.task_id} to {websocket.client} in /ws/subtitles endpoint"
                        )
                    except ValueError as e:
                        logger.error(
                            f"ValueError adding subtitle task via WebSocket /ws/subtitles endpoint for {validated_url}: {e}",
                            exc_info=True,
                        )
                        await websocket.send_json(
                            WebSocketSubtitleMessage(
                                type=MessageType.STATUS,
                                task_id="error",
                                status=TaskStatus.FAILED,
                                error=str(e),
                                client_uid=None,  # Don't include deprecated field in error responses
                            ).model_dump()
                        )

            except asyncio.TimeoutError:
                logger.debug(
                    f"Timeout waiting for client message from {websocket.client} for task {task_id} in /ws/subtitles endpoint. Sending ping."
                )
                if task_id:
                    await websocket.send_json({"type": "ping", "task_id": task_id})
                    logger.debug(
                        f"Sent ping to client {websocket.client} for task_id: {task_id} in /ws/subtitles endpoint"
                    )
                else:
                    await websocket.send_json({"type": "ping", "task_id": "initial"})
                    logger.debug(
                        f"Sent initial ping to client {websocket.client} in /ws/subtitles endpoint"
                    )
                continue

    except WebSocketDisconnect:
        logger.info(
            f"WebSocket disconnected: {websocket.client} from task {task_id if task_id else 'general session'} in /ws/subtitles endpoint"
        )
    except Exception as e:
        logger.error(
            f"WebSocket error in /ws/subtitles for {websocket.client}, task {task_id if task_id else 'N/A'}: {str(e)}",
            exc_info=True,
        )
    finally:
        logger.debug(
            f"Closing WebSocket /ws/subtitles for {websocket.client}. Task ID was: {task_id}"
        )
        await task_queue.ws_manager.disconnect_websocket(websocket)
        logger.debug(
            f"Called disconnect_websocket for {websocket.client} in /ws/subtitles endpoint finally block."
        )
