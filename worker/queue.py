import asyncio
import time
import hashlib
from typing import Dict, Optional
from loguru import logger

from models.schemas import (
    SubtitleResponse,
    SummarizeResponse,
    TaskStatus,
    VideoListResponse,
)
from worker.task_manager import TaskManager
# from worker.ws_notifier import WebSocketNotifier
# from worker.task_handlers import TaskHandler
# from worker.rate_limiter import YouTubeRateLimiter, SummarizeRateLimiter
# from worker.subtitles.youtube import YouTubeSubtitleDownloader
# from worker.summarizers.summarizer import TextSummarizer
from utils.proxy import ProxyManager


class TaskQueue:
    """Фасад для системы очередей задач"""

    def __init__(self):
        self._shutdown_started = False
        self.proxy_manager = ProxyManager()
        self.ws_manager = None  # Будет установлен позже
        self.task_manager = None  # Будет установлен позже

        # Для обратной совместимости
        self.tasks = {}  # Будет ссылаться на self.task_manager.tasks
        # Будет ссылаться на self.task_manager.active_async_tasks
        self.active_async_tasks = {}
        self.LIMIT_SUBTITLE_WORKERS = 0  # Будет установлено из task_manager
        self.LIMIT_SUMMARIZE_WORKERS = 0  # Будет установлено из task_manager

    def set_ws_manager(self, ws_manager):
        """Устанавливает WebSocket менеджер"""
        self.ws_manager = ws_manager
        if self.task_manager:
            self.task_manager.ws_manager = ws_manager

    async def initialize(self):
        """Инициализация компонентов"""
        logger.debug("TaskQueue initializing...")

        # Настройка прокси
        logger.debug("Setting up proxy manager...")
        is_proxy_connected = await self.proxy_manager.setup_proxy()
        logger.debug(f"Proxy connected: {is_proxy_connected}")

        # Создание и инициализация менеджера задач
        if not self.ws_manager:
            from utils.ws_manager import ConnectionManager
            self.ws_manager = ConnectionManager()

        self.task_manager = TaskManager(self.ws_manager)
        await self.task_manager.initialize()

        # Настройка прокси для загрузчика субтитров
        proxy_url = self.proxy_manager.get_yt_dlp_proxy() if is_proxy_connected else None
        await self.task_manager.task_handler.initialize(proxy_url)

        # Для обратной совместимости
        self.tasks = self.task_manager.tasks
        self.active_async_tasks = self.task_manager.active_async_tasks
        self.LIMIT_SUBTITLE_WORKERS = self.task_manager.LIMIT_SUBTITLE_WORKERS
        self.LIMIT_SUMMARIZE_WORKERS = self.task_manager.LIMIT_SUMMARIZE_WORKERS

        logger.debug("TaskQueue initialized successfully.")

    # Методы для обратной совместимости

    async def is_task_active(self, task_id: str) -> bool:
        """Проверяет, активна ли задача"""
        return await self.task_manager.is_task_active(task_id)

    async def cancel_task(self, task_id: str):
        """Отменяет выполняющуюся задачу или помечает ожидающую задачу для отмены"""
        await self.task_manager.cancel_task(task_id)

    async def notify_subscribers_of_cancellation(self, task_id: str, error_message: str):
        """Уведомляет подписчиков WebSocket о отмене задачи"""
        await self.task_manager.ws_notifier.notify_subscribers_of_cancellation(
            task_id, error_message, self.task_manager.tasks
        )

    async def notify_subscribers(self, task_id: str, message):
        """Отправляет обновление всем подписчикам WebSocket задачи"""
        await self.task_manager.ws_notifier.notify_subscribers(task_id, message)

    async def register_websocket(self, task_id: str, websocket):
        """Регистрирует WebSocket соединение для задачи"""
        if not self.ws_manager:
            logger.error(
                "WebSocket manager not initialized in register_websocket")
            return

        await self.ws_manager.connect(task_id, websocket)
        logger.debug(f"Registered WebSocket for task {task_id}")

    # Методы для добавления задач

    async def add_subtitle_task(self, video_id: str, url: str, client_uid: Optional[str] = None) -> SubtitleResponse:
        """Добавляет задачу получения субтитров в очередь"""
        task_obj = SubtitleResponse(
            task_id=video_id,
            status=TaskStatus.PENDING,
            client_uid=client_uid,
        )

        # Pass only URL to internal processing, client_uid is stored in task_obj
        success = await self.task_manager.add_subtitle_task(
            video_id, (url, None), task_obj
        )

        if not success:
            task_obj.status = TaskStatus.FAILED
            task_obj.error = "Failed to add task to queue"

        return task_obj

    async def add_video_list_task(self, url: str, client_uid: Optional[str] = None) -> VideoListResponse:
        """Добавляет задачу получения списка видео в очередь"""
        # Генерация уникального ID для задачи
        task_id = f"list_{hashlib.md5(url.encode()).hexdigest()[:8]}_{int(time.time())}"

        task_obj = VideoListResponse(
            task_id=task_id,
            status=TaskStatus.PENDING,
            client_uid=client_uid,
        )

        # Pass only URL to internal processing, client_uid is stored in task_obj
        success = await self.task_manager.add_video_list_task(
            task_id, (url, None), task_obj
        )

        if not success:
            task_obj.status = TaskStatus.FAILED
            task_obj.error = "Failed to add task to queue"

        return task_obj

    async def add_summarize_task(self, text: str, mode: str = "default", client_uid: Optional[str] = None) -> SummarizeResponse:
        """Добавляет задачу суммаризации в очередь"""
        # Генерация уникального ID для задачи
        task_id = f"summ_{hashlib.md5(text[:100].encode()).hexdigest()[:8]}_{int(time.time())}"

        task_obj = SummarizeResponse(
            task_id=task_id,
            status=TaskStatus.PENDING,
            client_uid=client_uid,
        )

        # Pass only text and mode to internal processing, client_uid is stored in task_obj
        success = await self.task_manager.add_summarize_task(
            task_id, (text, mode, None), task_obj
        )

        if not success:
            task_obj.status = TaskStatus.FAILED
            task_obj.error = "Failed to add task to queue"

        return task_obj

    # Методы для получения информации о задачах

    def get_task(self, task_id: str) -> Optional[SubtitleResponse | SummarizeResponse | VideoListResponse]:
        """Возвращает информацию о задаче по ID"""
        return self.task_manager.tasks.get(task_id)

    def get_queue_sizes(self) -> Dict[str, int]:
        """Возвращает размеры очередей"""
        if not hasattr(self.task_manager, 'queues'):
            return {}

        return {
            task_type: queue.qsize() if queue else 0
            for task_type, queue in self.task_manager.queues.items()
        }

    def get_active_tasks_count(self) -> Dict[str, int]:
        """Возвращает количество активных задач"""
        subtitle_count = self.task_manager.LIMIT_SUBTITLE_WORKERS - \
            self.task_manager.subtitle_semaphore._value
        summarize_count = self.task_manager.LIMIT_SUMMARIZE_WORKERS - \
            self.task_manager.summarize_semaphore._value

        return {
            "subtitle": max(0, subtitle_count),
            "summarize": max(0, summarize_count),
        }

    # Методы для очистки

    async def cleanup(self):
        """Очищает ресурсы при завершении работы"""
        logger.info("Cleaning up TaskQueue resources...")

        # Отмена всех активных задач
        for task_id, task in list(self.task_manager.active_async_tasks.items()):
            if not task.done():
                logger.debug(
                    f"Cancelling active task {task_id} during cleanup")
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    logger.debug(f"Task {task_id} cancelled successfully")
                except Exception as e:
                    logger.error(f"Error cancelling task {task_id}: {e}")

        # Очистка очередей
        while not self.task_manager.subtitle_queue.empty():
            await self.task_manager.subtitle_queue.get()
        while not self.task_manager.summarize_queue.empty():
            await self.task_manager.summarize_queue.get()
        while not self.task_manager.video_list_queue.empty():
            await self.task_manager.video_list_queue.get()

        logger.info("All queues cleared.")

    async def check_active_tasks(self) -> bool:
        """Проверяет наличие активных задач"""
        active_tasks = self.get_active_tasks_count()
        return active_tasks["subtitle"] > 0 or active_tasks["summarize"] > 0

    async def shutdown(self, force: bool = False):
        if self._shutdown_started:
            logger.info("TaskQueue shutdown already in progress or completed.")
            return True
        self._shutdown_started = True
        """Корректное завершение работы всех компонентов

        Args:
            force (bool): Если True, завершает работу без запроса подтверждения
        """
        # Проверка наличия активных задач
        if not force and await self.check_active_tasks():
            logger.warning("Обнаружены активные задачи!")
            response = input(
                "Обнаружены активные задачи. Завершить их? [Y/n]: ").strip().lower()

            if response in ['n', 'no']:
                logger.info(
                    "Отмена завершения работы, возвращаемся к нормальной работе.")
                return False

        logger.info("Initiating TaskQueue shutdown...")

        tasks_to_await_completion = []

        # Отмена всех активных задач
        logger.debug("Cancelling active async_tasks...")
        # Create a copy for safe iteration, as tasks might modify the dict upon cancellation/completion
        active_async_tasks_copy = list(
            self.task_manager.active_async_tasks.items())
        for task_id, task_obj in active_async_tasks_copy:
            if not task_obj.done():
                logger.debug(f"Cancelling active async_task {task_id}")
                task_obj.cancel()
                tasks_to_await_completion.append(task_obj)

        # Отмена всех worker manager задач
        logger.debug("Cancelling worker manager tasks...")
        for task_obj in self.task_manager.worker_manager_tasks:
            if not task_obj.done():
                task_name = getattr(task_obj, 'get_name',
                                    lambda: 'Unnamed worker task')()
                logger.debug(f"Cancelling worker manager task: {task_name}")
                task_obj.cancel()
                tasks_to_await_completion.append(task_obj)

        # Очистка WebSocket менеджера
        if self.ws_manager and hasattr(self.ws_manager, 'cleanup_task') and self.ws_manager.cleanup_task:
            ws_task = self.ws_manager.cleanup_task
            if not ws_task.done():
                logger.debug("Cancelling WebSocket cleanup task...")
                ws_task.cancel()
                tasks_to_await_completion.append(ws_task)

        # Ожидание завершения всех отмененных задач
        if tasks_to_await_completion:
            logger.info(
                f"Waiting for {len(tasks_to_await_completion)} background tasks to complete cancellation...")
            results = await asyncio.gather(*tasks_to_await_completion, return_exceptions=True)
            for idx, result in enumerate(results):
                task_ref = tasks_to_await_completion[idx]
                task_name = getattr(task_ref, 'get_name',
                                    lambda: f'Task {idx}')()
                if isinstance(result, asyncio.CancelledError):
                    logger.debug(
                        f"Task '{task_name}' was cancelled successfully during shutdown.")
                elif isinstance(result, Exception):
                    logger.warning(
                        f"Task '{task_name}' raised an exception during shutdown/cancellation: {result}", exc_info=False)
                else:
                    logger.debug(
                        f"Task '{task_name}' completed during shutdown.")
        else:
            logger.debug(
                "No background tasks required explicit awaiting during shutdown.")

        # Очистка всех ресурсов без ожидания завершения задач
        # Это позволит избежать блокировки при завершении работы
        try:
            logger.info("Clearing all queues...")

            # Clear all queues from the queues dictionary
            for queue_name, queue in self.task_manager.queues.items():
                logger.debug(f"Clearing queue: {queue_name}")
                while True:
                    try:
                        queue.get_nowait()
                    except asyncio.QueueEmpty:
                        break

            logger.info("All queues cleared.")
        except Exception as e:
            logger.error(f"Error clearing queues: {e}")

        # Завершение работы обработчика задач
        if hasattr(self.task_manager.task_handler, 'cleanup'):
            logger.debug("Cleaning up task handler...")
            try:
                await asyncio.wait_for(self.task_manager.task_handler.cleanup(), timeout=5.0)
            except asyncio.TimeoutError:
                logger.warning(
                    "Task handler cleanup timed out during shutdown.")
            except Exception as e:
                logger.error(f"Error cleaning up task handler: {e}")

        logger.info("TaskQueue shutdown completed.")
        return True
